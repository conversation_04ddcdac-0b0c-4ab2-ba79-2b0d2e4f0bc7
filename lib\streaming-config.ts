// Configuration pour optimiser le streaming des annotations
export const STREAMING_CONFIG = {
  // <PERSON><PERSON><PERSON> minimum entre les annotations pour éviter la surcharge
  ANNOTATION_THROTTLE_MS: 100,

  // Taille maximale du buffer d'annotations
  MAX_ANNOTATION_BUFFER: 50,

  // Timeout pour les annotations en attente
  ANNOTATION_TIMEOUT_MS: 5000,

  // Types d'annotations prioritaires (envoyées immédiatement)
  PRIORITY_ANNOTATION_TYPES: ['progress', 'timeline', 'notification', 'status'],

  // Configuration pour le flush automatique
  AUTO_FLUSH_INTERVAL_MS: 200,

  // Retry configuration pour les annotations échouées
  RETRY_CONFIG: {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2,
  },
};

// Utilitaire pour créer des annotations standardisées
export const createAnnotation = (
  type: string,
  data: any,
  options?: {
    priority?: boolean;
    transient?: boolean;
    id?: string;
  },
) => {
  return {
    type,
    data: {
      ...data,
      timestamp: Date.now(),
      id:
        options?.id ||
        `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    },
    ...(options?.priority && { priority: true }),
    ...(options?.transient && { transient: true }),
  };
};

// Queue pour gérer les annotations avec priorité
export class AnnotationQueue {
  private queue: Array<{
    annotation: any;
    priority: boolean;
    timestamp: number;
  }> = [];
  private processing = false;

  constructor(private sendFunction: (annotation: any) => void) {}

  enqueue(annotation: any, priority = false) {
    this.queue.push({
      annotation,
      priority,
      timestamp: Date.now(),
    });

    // Trier par priorité et timestamp
    this.queue.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority ? 1 : -1; // Priorité d'abord
      }
      return a.timestamp - b.timestamp; // Puis par ordre chronologique
    });

    this.processQueue();
  }

  private async processQueue() {
    if (this.processing || this.queue.length === 0) return;

    this.processing = true;

    while (this.queue.length > 0) {
      const item = this.queue.shift();
      if (!item) break;

      try {
        this.sendFunction(item.annotation);

        // Throttling pour éviter la surcharge
        if (!item.priority) {
          await new Promise((resolve) =>
            setTimeout(resolve, STREAMING_CONFIG.ANNOTATION_THROTTLE_MS),
          );
        }
      } catch (error) {
        console.error("Erreur lors de l'envoi de l'annotation:", error);

        // Retry logic pour les annotations importantes
        if (
          item.priority &&
          item.annotation.retryCount < STREAMING_CONFIG.RETRY_CONFIG.maxRetries
        ) {
          item.annotation.retryCount = (item.annotation.retryCount || 0) + 1;

          setTimeout(
            () => {
              this.queue.unshift(item);
            },
            STREAMING_CONFIG.RETRY_CONFIG.retryDelay *
              Math.pow(
                STREAMING_CONFIG.RETRY_CONFIG.backoffMultiplier,
                item.annotation.retryCount - 1,
              ),
          );
        }
      }
    }

    this.processing = false;
  }

  clear() {
    this.queue = [];
  }

  getQueueLength() {
    return this.queue.length;
  }
}
