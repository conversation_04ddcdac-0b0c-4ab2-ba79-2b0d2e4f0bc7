import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';
import remarkGfm from 'remark-gfm';

export function Markdown({ children }: { children: string }) {
  return (
    <div className="prose prose-invert max-w-none">
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
      components={{
        code({ className, children, node, ...props }) {
          const match = /language-(\w+)/.exec(className || '');
          const isInline =
            !node?.position?.start.line ||
            node.position.start.line === node.position.end.line;

          return !isInline && match ? (
            <div className="overflow-hidden rounded-md my-4">
              <SyntaxHighlighter
                style={oneDark}
                language={match[1]}
                showLineNumbers={true}
                wrapLines={true}
              >
                {String(children).replace(/\n$/, '')}
              </SyntaxHighlighter>
            </div>
          ) : (
            <code
              className={`bg-gray-800 px-1 py-0.5 rounded text-sm ${className || ''}`}
              {...props}
            >
              {children}
            </code>
          );
        },
        table({ children }) {
          return (
            <div className="overflow-x-auto my-4">
              <table className="border-collapse border border-gray-700 w-full">
                {children}
              </table>
            </div>
          );
        },
        th({ children }) {
          return (
            <th className="border border-gray-700 bg-gray-800 px-4 py-2 text-left">
              {children}
            </th>
          );
        },
        td({ children }) {
          return (
            <td className="border border-gray-700 px-4 py-2">{children}</td>
          );
        },
        pre({ children }) {
          return <>{children}</>;
        },
        a({ node, href, children, ...props }) {
          return (
            <a
              href={href}
              className="text-blue-500 hover:underline"
              target="_blank"
              rel="noopener noreferrer"
              {...props}
            >
              {children}
            </a>
          );
        },
      }}
      >
        {children}
      </ReactMarkdown>
    </div>
  );
}
