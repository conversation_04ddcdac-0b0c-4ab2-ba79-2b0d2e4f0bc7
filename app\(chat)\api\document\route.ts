import { auth } from '@/app/(auth)/auth';
import type { ArtifactKind } from '@/components/artifact';
import type { NextRequest } from 'next/server';
import {
  deleteDocumentsByIdAfterTimestamp,
  getDocumentsById,
  getDocumentById,
  saveDocument,
} from '@/lib/db/queries';
import { ChatSDKError } from '@/lib/errors';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new ChatSDKError('bad_request:document').toResponse();
  }

  // Special handling for 'init' ID which is used as a placeholder
  // and not a real document ID in the database
  if (id === 'init') {
    console.log(
      'Received request for placeholder document ID "init", returning empty array',
    );
    return Response.json([], { status: 200 });
  }

  const session = await auth();

  if (!session?.user?.id) {
    return new ChatSDKError('unauthorized:document').toResponse();
  }

  try {
    const documents = await getDocumentsById({ id });

    const [document] = documents;

    if (!document) {
      return new ChatSDKError('not_found:document').toResponse();
    }

    if (document.userId !== session.user.id) {
      return new ChatSDKError('forbidden:document').toResponse();
    }

    return Response.json(documents, { status: 200 });
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error(`Error fetching document with ID ${id}:`, error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

export async function POST(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new ChatSDKError('bad_request:document').toResponse();
  }

  // Special handling for 'init' ID which is used as a placeholder
  if (id === 'init') {
    console.log(
      'Received POST request for placeholder document ID "init", returning 400 Bad Request',
    );
    return new ChatSDKError(
      'bad_request:document',
      'Invalid document ID: "init" is a placeholder and cannot be used for saving documents',
    ).toResponse();
  }

  const session = await auth();

  if (!session) {
    return new ChatSDKError('unauthorized:document').toResponse();
  }

  try {
    const {
      content,
      title,
      kind,
    }: { content: string; title: string; kind: ArtifactKind } =
      await request.json();

    if (session.user?.id) {
      // First check if the document exists and belongs to the current user
      const existingDocument = await getDocumentById({ id });

      if (existingDocument && existingDocument.userId !== session.user.id) {
        return new ChatSDKError('forbidden:document').toResponse();
      }

      const document = await saveDocument({
        id,
        content,
        title,
        kind,
        userId: session.user.id,
      });

      return Response.json(document, { status: 200 });
    }

    return new ChatSDKError('unauthorized:document').toResponse();
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error(`Error saving document with ID ${id}:`, error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

export async function PATCH(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new ChatSDKError('bad_request:document').toResponse();
  }

  // Special handling for 'init' ID which is used as a placeholder
  if (id === 'init') {
    console.log(
      'Received PATCH request for placeholder document ID "init", returning 400 Bad Request',
    );
    return new ChatSDKError(
      'bad_request:document',
      'Invalid document ID: "init" is a placeholder and cannot be modified',
    ).toResponse();
  }

  const { timestamp }: { timestamp: string } = await request.json();

  if (!timestamp) {
    return new ChatSDKError(
      'bad_request:document',
      'Missing timestamp',
    ).toResponse();
  }

  const session = await auth();

  if (!session || !session.user) {
    return new ChatSDKError('unauthorized:document').toResponse();
  }

  try {
    // Vérifier si le document existe et appartient à l'utilisateur
    const documents = await getDocumentsById({ id });

    if (!documents || documents.length === 0) {
      return new ChatSDKError('not_found:document').toResponse();
    }

    const [document] = documents;

    if (document.userId !== session.user.id) {
      return new ChatSDKError('forbidden:document').toResponse();
    }

    // Vérifier si le timestamp est valide
    let parsedTimestamp: Date;
    try {
      // Si le timestamp est un nombre (timestamp Unix), le convertir en Date
      const timestampNumber = Number(timestamp);
      if (!Number.isNaN(timestampNumber)) {
        parsedTimestamp = new Date(timestampNumber);
      } else {
        // Sinon, essayer de le parser comme une chaîne de date
        parsedTimestamp = new Date(timestamp);
      }

      // Vérifier si la date est valide
      if (Number.isNaN(parsedTimestamp.getTime())) {
        throw new Error('Invalid timestamp format');
      }

      console.log('PATCH - Parsed timestamp:', parsedTimestamp.toISOString());
    } catch (error) {
      console.error(
        'PATCH - Error parsing timestamp:',
        error,
        'Timestamp value:',
        timestamp,
      );
      return new ChatSDKError(
        'bad_request:document',
        'Invalid timestamp format',
      ).toResponse();
    }

    // Supprimer le document en utilisant la fonction existante
    const documentsDeleted = await deleteDocumentsByIdAfterTimestamp({
      id,
      timestamp: parsedTimestamp,
    });

    // Vérifier si des documents ont été supprimés
    if (!documentsDeleted || documentsDeleted.length === 0) {
      return new ChatSDKError(
        'not_found:document',
        'No documents were deleted',
      ).toResponse();
    }

    return Response.json(documentsDeleted, { status: 200 });
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('PATCH - Error deleting document:', error);
    return new Response('An error occurred while deleting the document', {
      status: 500,
    });
  }
}

export async function DELETE(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const id = searchParams.get('id');
  const timestamp = searchParams.get('timestamp');

  // Validate required parameters
  if (!id) {
    return new ChatSDKError(
      'bad_request:document',
      'Missing document ID',
    ).toResponse();
  }

  // Special handling for 'init' ID which is used as a placeholder
  if (id === 'init') {
    console.log(
      'Received DELETE request for placeholder document ID "init", returning 400 Bad Request',
    );
    return new ChatSDKError(
      'bad_request:document',
      'Invalid document ID: "init" is a placeholder and cannot be deleted',
    ).toResponse();
  }

  // Ensure timestamp is required for deletion
  if (!timestamp) {
    return new ChatSDKError(
      'bad_request:document',
      'Missing timestamp',
    ).toResponse();
  }

  const session = await auth();

  if (!session || !session.user) {
    return new ChatSDKError('unauthorized:document').toResponse();
  }

  try {
    // Vérifier si le document existe et appartient à l'utilisateur
    const documents = await getDocumentsById({ id });

    if (!documents || documents.length === 0) {
      return new ChatSDKError('not_found:document').toResponse();
    }

    const [document] = documents;

    if (document.userId !== session.user.id) {
      return new ChatSDKError('forbidden:document').toResponse();
    }

    // Vérifier si le timestamp est valide
    let parsedTimestamp: Date;
    try {
      // Si le timestamp est un nombre (timestamp Unix), le convertir en Date
      const timestampNumber = Number(timestamp);
      if (!Number.isNaN(timestampNumber)) {
        parsedTimestamp = new Date(timestampNumber);
      } else {
        // Sinon, essayer de le parser comme une chaîne de date
        parsedTimestamp = new Date(timestamp);
      }

      // Vérifier si la date est valide
      if (Number.isNaN(parsedTimestamp.getTime())) {
        throw new Error('Invalid timestamp format');
      }

      console.log('Parsed timestamp:', parsedTimestamp.toISOString());
    } catch (error) {
      console.error(
        'Error parsing timestamp:',
        error,
        'Timestamp value:',
        timestamp,
      );
      return new ChatSDKError(
        'bad_request:document',
        'Invalid timestamp format',
      ).toResponse();
    }

    // Supprimer le document en utilisant la fonction existante
    const deletedDocuments = await deleteDocumentsByIdAfterTimestamp({
      id,
      timestamp: parsedTimestamp,
    });

    // Vérifier si des documents ont été supprimés
    if (!deletedDocuments || deletedDocuments.length === 0) {
      return new ChatSDKError(
        'not_found:document',
        'No documents were deleted',
      ).toResponse();
    }

    return new Response('Document deleted', { status: 200 });
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('Error deleting document:', error);
    return new Response('An error occurred while deleting the document', {
      status: 500,
    });
  }
}
