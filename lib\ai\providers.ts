import {
  customProvider,
  extractReasoningMiddleware,
  wrapLanguageModel,
} from 'ai';
import { xai } from '@ai-sdk/xai';
import { isTestEnvironment } from '../constants';
import { google } from '@ai-sdk/google';
import {
  artifactModel,
  chatModel,
  reasoningModel,
  titleModel,
} from './models.test';

// Configuration des paramètres de sécurité pour Google Generative AI
const safetySettings = [
  {
    category: 'HARM_CATEGORY_HATE_SPEECH' as const,
    threshold: 'BLOCK_MEDIUM_AND_ABOVE' as const,
  },
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT' as const,
    threshold: 'BLOCK_MEDIUM_AND_ABOVE' as const,
  },
  {
    category: 'HARM_CATEGORY_HARASSMENT' as const,
    threshold: 'BLOCK_MEDIUM_AND_ABOVE' as const,
  },
  {
    category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT' as const,
    threshold: 'BLOCK_MEDIUM_AND_ABOVE' as const,
  },
];

// Création d'une fonction pour obtenir le modèle Google avec les paramètres de sécurité
const getGoogleModel = (modelName = 'gemini-2.0-flash-001') => {
  const model = google(modelName);
  // Wrap the model to automatically include safety settings
  return {
    ...model,
    doGenerate: async (options: any) => {
      return model.doGenerate({
        ...options,
        providerOptions: {
          ...options.providerOptions,
          google: {
            ...options.providerOptions?.google,
            safetySettings,
          },
        },
      });
    },
    doStream: async (options: any) => {
      return model.doStream({
        ...options,
        providerOptions: {
          ...options.providerOptions,
          google: {
            ...options.providerOptions?.google,
            safetySettings,
          },
        },
      });
    },
  };
};

export const myProvider = isTestEnvironment
  ? customProvider({
      languageModels: {
        'chat-model': chatModel,
        'chat-model-reasoning': reasoningModel,
        'title-model': titleModel,
        'artifact-model': artifactModel,
        'extreme-search-model': chatModel, // Use test model for extreme search
      },
    })
  : customProvider({
      languageModels: {
        'chat-model': getGoogleModel('gemini-2.0-flash-001'), // gemini-2.5-pro
        'chat-model-reasoning': wrapLanguageModel({
          model: getGoogleModel('gemini-2.0-flash-001'),
          middleware: extractReasoningMiddleware({ tagName: 'think' }),
        }),
        'title-model': getGoogleModel('gemini-2.0-flash-001'),
        'artifact-model': getGoogleModel('gemini-2.0-flash-001'),
        'extreme-search-model': xai('grok-3-mini-fast'), // Use XAI for extreme search to avoid Gemini function call issues
        'x-fast': xai('grok-3-fast'),
      },
      imageModels: {
        'small-model': xai.image('grok-2-image'),
      },
    });
