import { NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/app/(auth)/auth';
import { isTestEnvironment } from '@/lib/constants';
import { createUploader } from '@/lib/blobUploader';
import { mockPut } from '@/lib/blobUploader.mock';
import { ChatSDKError } from '@/lib/errors';

// Déterminer quelle fonction d'upload utiliser
const putFunction =
  process.env.BLOB_MOCK === 'True' || isTestEnvironment ? mockPut : undefined;

// Créer l'uploader avec injection de dépendances
const uploader = createUploader(putFunction);

// Use Blob instead of File since File is not available in Node.js environment
const FileSchema = z.object({
  file: z
    .instanceof(Blob)
    .refine((file) => file.size <= 5 * 1024 * 1024, {
      message: 'File size should be less than 5MB',
    })
    // Accepter plus de types de fichiers
    .refine(
      (file) =>
        ['image/jpeg', 'image/png', 'application/pdf', 'text/plain'].includes(
          file.type,
        ),
      {
        message: 'File type should be JPEG, PNG, PDF or TXT',
      },
    ),
});

export async function POST(request: Request) {
  try {
    const session = await auth();

    if (!session) {
      return new ChatSDKError('unauthorized:api').toResponse();
    }

    if (request.body === null) {
      return new ChatSDKError(
        'bad_request:api',
        'Request body is empty',
      ).toResponse();
    }

    const formData = await request.formData();
    const file = formData.get('file') as Blob;

    if (!file) {
      return new ChatSDKError(
        'bad_request:api',
        'No file uploaded',
      ).toResponse();
    }

    // Vérifier si le token Blob est configuré
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      console.error('BLOB_READ_WRITE_TOKEN is not configured');
      return NextResponse.json(
        { error: 'Storage configuration error' },
        { status: 500 },
      );
    }

    const validatedFile = FileSchema.safeParse({ file });

    if (!validatedFile.success) {
      const errorMessage = validatedFile.error.errors
        .map((error) => error.message)
        .join(', ');

      return new ChatSDKError('bad_request:api', errorMessage).toResponse();
    }

    // Get filename from formData since Blob doesn't have name property
    const filename = (formData.get('file') as File).name;
    const fileBuffer = await file.arrayBuffer();

    // Ajouter un préfixe unique pour éviter les collisions
    const uniqueFilename = `${Date.now()}-${filename}`;

    const data = await uploader(uniqueFilename, fileBuffer, {
      access: 'public',
    });

    return NextResponse.json(data);
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 },
    );
  }
}
