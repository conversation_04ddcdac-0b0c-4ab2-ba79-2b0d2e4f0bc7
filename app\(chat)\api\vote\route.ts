import { auth } from '@/app/(auth)/auth';
import { getChatById, getVotesByChatId, voteMessage } from '@/lib/db/queries';
import { ChatSDKError } from '@/lib/errors';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new ChatSDKError(
      'bad_request:vote',
      'chatId is required',
    ).toResponse();
  }

  const session = await auth();

  if (!session || !session.user || !session.user.email) {
    return new ChatSDKError('unauthorized:vote').toResponse();
  }

  const chat = await getChatById({ id: chatId });

  if (!chat) {
    return new ChatSDKError('not_found:vote', 'Chat not found').toResponse();
  }

  if (chat.userId !== session.user.id) {
    return new ChatSDKError('forbidden:vote').toResponse();
  }

  const votes = await getVotesByChatId({ id: chatId });

  return Response.json(votes, { status: 200 });
}

export async function PATCH(request: Request) {
  try {
    const { chatId, messageId, type } = await request.json();

    if (!chatId || !messageId || !type) {
      return new ChatSDKError(
        'bad_request:vote',
        'messageId and type are required',
      ).toResponse();
    }

    const session = await auth();

    if (!session || !session.user || !session.user.email) {
      return new ChatSDKError('unauthorized:vote').toResponse();
    }

    const chat = await getChatById({ id: chatId });

    if (!chat) {
      return new ChatSDKError('not_found:vote', 'Chat not found').toResponse();
    }

    if (chat.userId !== session.user.id) {
      return new ChatSDKError('forbidden:vote').toResponse();
    }

    await voteMessage({
      chatId,
      messageId,
      type: type,
    });

    return new Response('Message voted', { status: 200 });
  } catch (error) {
    if (error instanceof ChatSDKError) {
      return error.toResponse();
    }
    console.error('Error parsing request body:', error);
    return new ChatSDKError(
      'bad_request:vote',
      'Invalid JSON in request body',
    ).toResponse();
  }
}
