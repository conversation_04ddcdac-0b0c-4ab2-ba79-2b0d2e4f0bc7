'use client';

import React, { useState, useRef, useEffect, type ReactNode } from 'react';
import { X, Minus, Maximize2, Minimize2, Move } from 'lucide-react';

interface FloatingWidgetProps {
  id: string;
  title: string;
  children: ReactNode;
  initialPosition?: { x: number; y: number };
  initialSize?: { width: number; height: number };
  onClose: () => void;
  onMinimize?: () => void;
  zIndex: number;
  onFocus: () => void;
}

export default function FloatingWidget({
  id,
  title,
  children,
  initialPosition = { x: 100, y: 100 },
  initialSize = { width: 600, height: 500 },
  onClose,
  onMinimize,
  zIndex,
  onFocus,
}: FloatingWidgetProps) {
  const [position, setPosition] = useState(initialPosition);
  const [size, setSize] = useState(initialSize);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isMaximized, setIsMaximized] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);

  // Utiliser useRef pour éviter les re-renders pendant le drag
  const dragStartRef = useRef({ x: 0, y: 0 });
  const resizeStartRef = useRef({ x: 0, y: 0, width: 0, height: 0 });
  const currentPositionRef = useRef(initialPosition);
  const currentSizeRef = useRef(initialSize);
  const animationFrameRef = useRef<number>();

  const widgetRef = useRef<HTMLDivElement>(null);

  // Mettre à jour les refs quand les états changent
  useEffect(() => {
    currentPositionRef.current = position;
  }, [position]);

  useEffect(() => {
    currentSizeRef.current = size;
  }, [size]);

  // Handle dragging avec optimisation
  const handleMouseDown = (e: React.MouseEvent) => {
    if (
      e.target === e.currentTarget ||
      (e.target as HTMLElement).classList.contains('drag-handle')
    ) {
      setIsDragging(true);
      dragStartRef.current = {
        x: e.clientX - currentPositionRef.current.x,
        y: e.clientY - currentPositionRef.current.y,
      };
      onFocus();

      // Ajouter une classe pour désactiver la sélection de texte
      document.body.style.userSelect = 'none';
      document.body.style.cursor = 'move';
    }
  };

  // Handle resizing avec optimisation
  const handleResizeMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsResizing(true);
    resizeStartRef.current = {
      x: e.clientX,
      y: e.clientY,
      width: currentSizeRef.current.width,
      height: currentSizeRef.current.height,
    };
    onFocus();

    // Ajouter une classe pour désactiver la sélection de texte
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'se-resize';
  };

  // Mouse move handler ultra-optimisé pour éliminer les saccades
  useEffect(() => {
    let lastMoveTime = 0;
    const THROTTLE_MS = 8; // ~120 FPS max pour éviter la surcharge

    const handleMouseMove = (e: MouseEvent) => {
      const now = performance.now();

      // Throttling pour éviter trop d'appels
      if (now - lastMoveTime < THROTTLE_MS) {
        return;
      }
      lastMoveTime = now;

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        if (isDragging && !isMaximized) {
          const newPosition = {
            x: e.clientX - dragStartRef.current.x,
            y: e.clientY - dragStartRef.current.y,
          };

          // Contraintes plus souples pour éviter les à-coups
          const constrainedPosition = {
            x: Math.max(-50, Math.min(window.innerWidth - 150, newPosition.x)),
            y: Math.max(-10, Math.min(window.innerHeight - 30, newPosition.y)),
          };

          // Mise à jour directe du style pour éviter les re-renders
          if (widgetRef.current) {
            widgetRef.current.style.left = `${constrainedPosition.x}px`;
            widgetRef.current.style.top = `${constrainedPosition.y}px`;
          }

          currentPositionRef.current = constrainedPosition;
        }

        if (isResizing && !isMaximized) {
          const newWidth = Math.max(
            300,
            resizeStartRef.current.width +
              (e.clientX - resizeStartRef.current.x),
          );
          const newHeight = Math.max(
            200,
            resizeStartRef.current.height +
              (e.clientY - resizeStartRef.current.y),
          );

          // Mise à jour directe du style pour le redimensionnement
          if (widgetRef.current) {
            widgetRef.current.style.width = `${newWidth}px`;
            widgetRef.current.style.height = `${newHeight}px`;
          }

          currentSizeRef.current = { width: newWidth, height: newHeight };
        }
      });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      setIsResizing(false);

      // Synchroniser l'état React avec les styles directs
      if (currentPositionRef.current) {
        setPosition(currentPositionRef.current);
      }
      if (currentSizeRef.current) {
        setSize(currentSizeRef.current);
      }

      // Restaurer les styles du body
      document.body.style.userSelect = '';
      document.body.style.cursor = '';

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };

    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove, {
        passive: true,
      });
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isDragging, isResizing, isMaximized]);

  const handleMinimize = () => {
    setIsMinimized(!isMinimized);
    if (onMinimize) onMinimize();
  };

  const handleMaximize = () => {
    setIsMaximized(!isMaximized);
  };

  const handleFocus = () => {
    onFocus();
  };

  const windowStyle = isMaximized
    ? {
        position: 'fixed' as const,
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex,
      }
    : {
        position: 'fixed' as const,
        left: position.x,
        top: position.y,
        width: size.width,
        height: isMinimized ? 'auto' : size.height,
        zIndex,
      };

  return (
    <div
      ref={widgetRef}
      className={`floating-widget bg-white border border-gray-300 rounded-lg shadow-lg overflow-hidden ${
        isDragging ? 'dragging' : ''
      } ${isResizing ? 'resizing' : ''}`}
      style={windowStyle}
      role="dialog"
      aria-label={`Floating widget: ${title}`}
      onMouseDown={handleFocus}
    >
      {/* Title Bar */}
      <div
        className="drag-handle flex items-center justify-between bg-gray-100 border-b border-gray-200 px-3 py-2 cursor-move select-none"
        role="button"
        tabIndex={0}
        aria-label={`Drag to move ${title} widget`}
        onMouseDown={handleMouseDown}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleMouseDown(e as any);
          }
        }}
      >
        <div className="flex items-center space-x-2">
          <Move size={14} className="text-gray-500" />
          <span className="text-sm font-medium text-gray-700 truncate">
            {title}
          </span>
        </div>

        <div className="flex items-center space-x-1">
          <button
            type="button"
            onClick={handleMinimize}
            className="p-1 hover:bg-gray-200 rounded transition-colors"
            title="Minimize"
          >
            <Minus size={14} className="text-gray-600" />
          </button>

          <button
            type="button"
            onClick={handleMaximize}
            className="p-1 hover:bg-gray-200 rounded transition-colors"
            title={isMaximized ? 'Restore' : 'Maximize'}
          >
            {isMaximized ? (
              <Minimize2 size={14} className="text-gray-600" />
            ) : (
              <Maximize2 size={14} className="text-gray-600" />
            )}
          </button>

          <button
            type="button"
            onClick={onClose}
            className="p-1 hover:bg-red-100 hover:text-red-600 rounded transition-colors"
            title="Close"
          >
            <X size={14} />
          </button>
        </div>
      </div>

      {/* Content */}
      {!isMinimized && (
        <div className="widget-content h-full overflow-hidden">{children}</div>
      )}

      {/* Resize Handle */}
      {!isMinimized && !isMaximized && (
        <div
          className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize bg-gray-300 hover:bg-gray-400 transition-colors"
          role="button"
          tabIndex={0}
          aria-label={`Resize ${title} widget`}
          onMouseDown={handleResizeMouseDown}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleResizeMouseDown(e as any);
            }
          }}
          style={{
            background:
              'linear-gradient(-45deg, transparent 30%, #9ca3af 30%, #9ca3af 70%, transparent 70%)',
          }}
        />
      )}
    </div>
  );
}
